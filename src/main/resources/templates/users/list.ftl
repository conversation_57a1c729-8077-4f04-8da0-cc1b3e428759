<#assign pageTitle = "User Management">
<#assign currentPage = "list">

<#include "../layout/base.ftl">

<@base>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users me-2"></i>User Management</h2>
        <a href="/users/signup" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i>Add New User
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <div class="stats-number">${totalUsers}</div>
                    <div>Total Users</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <div class="stats-number">${adminCount}</div>
                    <div>Administrators</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <div class="stats-number">${userCount}</div>
                    <div>Regular Users</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <div class="stats-number">${moderatorCount}</div>
                    <div>Moderators</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="/users/list" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search by Username</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" 
                               class="form-control" 
                               id="search" 
                               name="search" 
                               value="${search!''}" 
                               placeholder="Enter username...">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="role" class="form-label">Filter by Role</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">All Roles</option>
                        <#list roles as role>
                            <option value="${role.name()}" <#if selectedRole?? && selectedRole.name() == role.name()>selected</#if>>
                                ${role.displayName}
                            </option>
                        </#list>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                    <a href="/users/list" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-table me-2"></i>Users List
                <#if search?? && search?has_content>
                    <small class="text-muted">- Search results for "${search}"</small>
                <#elseif selectedRole??>
                    <small class="text-muted">- Filtered by ${selectedRole.displayName}</small>
                </#if>
            </h5>
        </div>
        <div class="card-body p-0">
            <#if users?? && users?size gt 0>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <#list users as user>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">#${user.id}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <strong>${user.username}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <i class="fas fa-envelope me-1 text-muted"></i>${user.email}
                                    </td>
                                    <td>
                                        <#switch user.role.name()>
                                            <#case "ADMIN">
                                                <span class="badge bg-danger">${user.role.displayName}</span>
                                                <#break>
                                            <#case "MODERATOR">
                                                <span class="badge bg-warning">${user.role.displayName}</span>
                                                <#break>
                                            <#default>
                                                <span class="badge bg-primary">${user.role.displayName}</span>
                                        </#switch>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/users/profile/${user.id}" 
                                               class="btn btn-outline-primary" 
                                               title="View Profile">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-outline-danger" 
                                                    title="Delete User"
                                                    onclick="confirmDelete(${user.id}, '${user.username}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </#list>
                        </tbody>
                    </table>
                </div>
            <#else>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No users found</h5>
                    <p class="text-muted">
                        <#if search?? && search?has_content>
                            No users match your search criteria.
                        <#elseif selectedRole??>
                            No users found with the selected role.
                        <#else>
                            There are no users in the system yet.
                        </#if>
                    </p>
                    <a href="/users/signup" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i>Add First User
                    </a>
                </div>
            </#if>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Deletion
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the user <strong id="deleteUsername"></strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <form id="deleteForm" method="post" style="display: inline;">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete User
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <#assign additionalJS>
        <script>
            function confirmDelete(userId, username) {
                document.getElementById('deleteUsername').textContent = username;
                document.getElementById('deleteForm').action = '/users/delete/' + userId;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            // Auto-submit form when role filter changes
            document.getElementById('role').addEventListener('change', function() {
                this.form.submit();
            });
        </script>
    </#assign>

    <#assign additionalCSS>
        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            .table td {
                vertical-align: middle;
            }
        </style>
    </#assign>
</@base>
