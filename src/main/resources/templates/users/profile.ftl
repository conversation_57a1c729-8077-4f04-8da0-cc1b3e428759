<#assign pageTitle = "User Profile - ${user.username}">

<#include "../layout/base.ftl">

<@base>
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user me-2"></i>User Profile</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/users/list">Users</a></li>
                    <li class="breadcrumb-item active">${user.username}</li>
                </ol>
            </nav>
        </div>
        <a href="/users/list" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Back to List
        </a>
    </div>

    <div class="row">
        <!-- User Information Card -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    <h4 class="card-title">${user.username}</h4>
                    <p class="text-muted">${user.email}</p>
                    
                    <div class="mb-3">
                        <#switch user.role.name()>
                            <#case "ADMIN">
                                <span class="badge bg-danger fs-6">${user.role.displayName}</span>
                                <#break>
                            <#case "MODERATOR">
                                <span class="badge bg-warning fs-6">${user.role.displayName}</span>
                                <#break>
                            <#default>
                                <span class="badge bg-primary fs-6">${user.role.displayName}</span>
                        </#switch>
                    </div>

                    <div class="row text-center">
                        <div class="col">
                            <div class="border-end">
                                <h5 class="mb-0">#${user.id}</h5>
                                <small class="text-muted">User ID</small>
                            </div>
                        </div>
                        <div class="col">
                            <h5 class="mb-0">
                                <#switch user.role.name()>
                                    <#case "ADMIN">
                                        <i class="fas fa-crown text-warning"></i>
                                        <#break>
                                    <#case "MODERATOR">
                                        <i class="fas fa-shield-alt text-info"></i>
                                        <#break>
                                    <#default>
                                        <i class="fas fa-user text-primary"></i>
                                </#switch>
                            </h5>
                            <small class="text-muted">Role</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="toggleEditMode()">
                            <i class="fas fa-edit me-1"></i>Edit Profile
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="showPasswordModal()">
                            <i class="fas fa-key me-1"></i>Change Password
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete(${user.id}, '${user.username}')">
                            <i class="fas fa-trash me-1"></i>Delete User
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Profile Details
                    </h5>
                </div>
                <div class="card-body">
                    <!-- View Mode -->
                    <div id="viewMode">
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong><i class="fas fa-user me-2"></i>Username:</strong>
                            </div>
                            <div class="col-sm-9">
                                ${user.username}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong><i class="fas fa-envelope me-2"></i>Email:</strong>
                            </div>
                            <div class="col-sm-9">
                                ${user.email}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong><i class="fas fa-user-tag me-2"></i>Role:</strong>
                            </div>
                            <div class="col-sm-9">
                                ${user.role.displayName}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong><i class="fas fa-id-badge me-2"></i>User ID:</strong>
                            </div>
                            <div class="col-sm-9">
                                #${user.id}
                            </div>
                        </div>
                    </div>

                    <!-- Edit Mode -->
                    <div id="editMode" style="display: none;">
                        <form method="post" action="/users/profile/${user.id}" id="profileForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>Username <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       value="${user.username}" 
                                       required 
                                       minlength="3"
                                       maxlength="50">
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       value="${user.email}" 
                                       required>
                            </div>

                            <div class="mb-3">
                                <label for="role" class="form-label">
                                    <i class="fas fa-user-tag me-1"></i>Role <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <#list roles as role>
                                        <option value="${role.name()}" <#if user.role.name() == role.name()>selected</#if>>
                                            ${role.displayName}
                                        </option>
                                    </#list>
                                </select>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Save Changes
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="toggleEditMode()">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Account Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border-end">
                                <h4 class="text-primary">Active</h4>
                                <small class="text-muted">Account Status</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border-end">
                                <h4 class="text-success">Verified</h4>
                                <small class="text-muted">Email Status</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h4 class="text-info">Standard</h4>
                            <small class="text-muted">Access Level</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Deletion
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the user <strong id="deleteUsername"></strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <form id="deleteForm" method="post" style="display: inline;">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i>Delete User
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Change Modal -->
    <div class="modal fade" id="passwordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>Change Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="passwordForm">
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" required minlength="6">
                        </div>
                        <div class="mb-3">
                            <label for="confirmNewPassword" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirmNewPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="changePassword()">
                        <i class="fas fa-save me-1"></i>Change Password
                    </button>
                </div>
            </div>
        </div>
    </div>

    <#assign additionalJS>
        <script>
            function toggleEditMode() {
                const viewMode = document.getElementById('viewMode');
                const editMode = document.getElementById('editMode');
                
                if (viewMode.style.display === 'none') {
                    viewMode.style.display = 'block';
                    editMode.style.display = 'none';
                } else {
                    viewMode.style.display = 'none';
                    editMode.style.display = 'block';
                }
            }

            function confirmDelete(userId, username) {
                document.getElementById('deleteUsername').textContent = username;
                document.getElementById('deleteForm').action = '/users/delete/' + userId;
                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            }

            function showPasswordModal() {
                new bootstrap.Modal(document.getElementById('passwordModal')).show();
            }

            function changePassword() {
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmNewPassword').value;
                
                if (newPassword !== confirmPassword) {
                    alert('Passwords do not match!');
                    return;
                }
                
                if (newPassword.length < 6) {
                    alert('Password must be at least 6 characters long!');
                    return;
                }
                
                // Here you would typically make an AJAX call to update the password
                alert('Password change functionality would be implemented here');
                bootstrap.Modal.getInstance(document.getElementById('passwordModal')).hide();
            }
        </script>
    </#assign>

    <#assign additionalCSS>
        <style>
            .avatar-lg {
                width: 80px;
                height: 80px;
                font-size: 24px;
            }
            .border-end {
                border-right: 1px solid #dee2e6 !important;
            }
        </style>
    </#assign>
</@base>
