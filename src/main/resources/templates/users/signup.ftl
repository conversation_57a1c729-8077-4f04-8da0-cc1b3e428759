<#assign pageTitle = "User Registration">
<#assign currentPage = "signup">

<#include "../layout/base.ftl">

<@base>
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>Create New User
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" action="/users/signup" id="signupForm" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>Username <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="username" 
                                           name="username" 
                                           required 
                                           minlength="3"
                                           maxlength="50"
                                           placeholder="Enter username">
                                    <div class="invalid-feedback">
                                        Please provide a valid username (3-50 characters).
                                    </div>
                                    <div class="form-text" id="usernameHelp"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           required 
                                           placeholder="Enter email address">
                                    <div class="invalid-feedback">
                                        Please provide a valid email address.
                                    </div>
                                    <div class="form-text" id="emailHelp"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Password <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" 
                                               class="form-control" 
                                               id="password" 
                                               name="password" 
                                               required 
                                               minlength="6"
                                               placeholder="Enter password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        Password must be at least 6 characters long.
                                    </div>
                                    <div class="form-text">
                                        <small>Password should be at least 6 characters long.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Confirm Password <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" 
                                               class="form-control" 
                                               id="confirmPassword" 
                                               name="confirmPassword" 
                                               required 
                                               placeholder="Confirm password">
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        Passwords do not match.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="role" class="form-label">
                                <i class="fas fa-user-tag me-1"></i>Role
                            </label>
                            <select class="form-select" id="role" name="role">
                                <option value="">Select a role (default: User)</option>
                                <#list roles as role>
                                    <option value="${role.name()}">${role.displayName}</option>
                                </#list>
                            </select>
                            <div class="form-text">
                                If no role is selected, the user will be assigned the "User" role by default.
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="/users/list" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-user-plus me-1"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <#assign additionalJS>
        <script>
            // Form validation
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();

            // Password visibility toggle
            document.getElementById('togglePassword').addEventListener('click', function() {
                const password = document.getElementById('password');
                const icon = this.querySelector('i');
                if (password.type === 'password') {
                    password.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    password.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });

            document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
                const confirmPassword = document.getElementById('confirmPassword');
                const icon = this.querySelector('i');
                if (confirmPassword.type === 'password') {
                    confirmPassword.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    confirmPassword.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });

            // Password confirmation validation
            document.getElementById('confirmPassword').addEventListener('input', function() {
                const password = document.getElementById('password').value;
                const confirmPassword = this.value;
                
                if (password !== confirmPassword) {
                    this.setCustomValidity('Passwords do not match');
                } else {
                    this.setCustomValidity('');
                }
            });

            // Username availability check
            let usernameTimeout;
            document.getElementById('username').addEventListener('input', function() {
                const username = this.value.trim();
                const helpText = document.getElementById('usernameHelp');
                
                if (username.length >= 3) {
                    clearTimeout(usernameTimeout);
                    usernameTimeout = setTimeout(() => {
                        fetch(`/users/check-username?username=${encodeURIComponent(username)}`)
                            .then(response => response.json())
                            .then(available => {
                                if (available) {
                                    helpText.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> Username is available</span>';
                                } else {
                                    helpText.innerHTML = '<span class="text-danger"><i class="fas fa-times"></i> Username is already taken</span>';
                                }
                            })
                            .catch(() => {
                                helpText.innerHTML = '';
                            });
                    }, 500);
                } else {
                    helpText.innerHTML = '';
                }
            });

            // Email availability check
            let emailTimeout;
            document.getElementById('email').addEventListener('input', function() {
                const email = this.value.trim();
                const helpText = document.getElementById('emailHelp');
                
                if (email.includes('@') && email.includes('.')) {
                    clearTimeout(emailTimeout);
                    emailTimeout = setTimeout(() => {
                        fetch(`/users/check-email?email=${encodeURIComponent(email)}`)
                            .then(response => response.json())
                            .then(available => {
                                if (available) {
                                    helpText.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> Email is available</span>';
                                } else {
                                    helpText.innerHTML = '<span class="text-danger"><i class="fas fa-times"></i> Email is already registered</span>';
                                }
                            })
                            .catch(() => {
                                helpText.innerHTML = '';
                            });
                    }, 500);
                } else {
                    helpText.innerHTML = '';
                }
            });
        </script>
    </#assign>
</@base>
