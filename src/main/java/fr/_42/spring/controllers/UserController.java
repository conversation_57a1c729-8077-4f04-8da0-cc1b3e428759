package fr._42.spring.controllers;

import fr._42.spring.models.User;
import fr._42.spring.models.Role;
import fr._42.spring.services.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/users")
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * Show user registration form
     */
    @GetMapping("/signup")
    public String showSignupForm(Model model) {
        model.addAttribute("user", new User());
        model.addAttribute("roles", Role.values());
        return "users/signup";
    }

    /**
     * Process user registration
     */
    @PostMapping("/signup")
    public String processSignup(@RequestParam String username,
                               @RequestParam String password,
                               @RequestParam String confirmPassword,
                               @RequestParam String email,
                               @RequestParam(required = false) Role role,
                               RedirectAttributes redirectAttributes) {
        try {
            // Validate passwords match
            if (!password.equals(confirmPassword)) {
                redirectAttributes.addFlashAttribute("error", "Passwords do not match");
                return "redirect:/users/signup";
            }

            // Validate required fields
            if (username.trim().isEmpty() || password.trim().isEmpty() || email.trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "All fields are required");
                return "redirect:/users/signup";
            }

            // Create user
            User user = userService.createUser(username.trim(), password, email.trim(), role);
            redirectAttributes.addFlashAttribute("success", 
                "User '" + user.getUsername() + "' created successfully!");
            return "redirect:/users/list";

        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/users/signup";
        }
    }

    /**
     * Show all users
     */
    @GetMapping("/list")
    public String listUsers(@RequestParam(required = false) String search,
                           @RequestParam(required = false) Role role,
                           Model model) {
        List<User> users;
        
        if (search != null && !search.trim().isEmpty()) {
            users = userService.searchUsersByUsername(search.trim());
            model.addAttribute("search", search);
        } else if (role != null) {
            users = userService.getUsersByRole(role);
            model.addAttribute("selectedRole", role);
        } else {
            users = userService.getAllUsers();
        }

        model.addAttribute("users", users);
        model.addAttribute("roles", Role.values());
        
        // Add statistics
        model.addAttribute("totalUsers", userService.getAllUsers().size());
        model.addAttribute("adminCount", userService.getUserCountByRole(Role.ADMIN));
        model.addAttribute("userCount", userService.getUserCountByRole(Role.USER));
        model.addAttribute("moderatorCount", userService.getUserCountByRole(Role.MODERATOR));
        
        return "users/list";
    }

    /**
     * Show user profile
     */
    @GetMapping("/profile/{id}")
    public String showProfile(@PathVariable Long id, Model model, RedirectAttributes redirectAttributes) {
        Optional<User> userOpt = userService.getUserById(id);
        if (userOpt.isPresent()) {
            model.addAttribute("user", userOpt.get());
            model.addAttribute("roles", Role.values());
            return "users/profile";
        } else {
            redirectAttributes.addFlashAttribute("error", "User not found");
            return "redirect:/users/list";
        }
    }

    /**
     * Update user profile
     */
    @PostMapping("/profile/{id}")
    public String updateProfile(@PathVariable Long id,
                               @RequestParam String username,
                               @RequestParam String email,
                               @RequestParam Role role,
                               RedirectAttributes redirectAttributes) {
        try {
            User updatedUser = userService.updateUser(id, username.trim(), email.trim(), role);
            redirectAttributes.addFlashAttribute("success", 
                "User '" + updatedUser.getUsername() + "' updated successfully!");
            return "redirect:/users/profile/" + id;
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/users/profile/" + id;
        }
    }

    /**
     * Delete user
     */
    @PostMapping("/delete/{id}")
    public String deleteUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            Optional<User> userOpt = userService.getUserById(id);
            if (userOpt.isPresent()) {
                String username = userOpt.get().getUsername();
                userService.deleteUser(id);
                redirectAttributes.addFlashAttribute("success", 
                    "User '" + username + "' deleted successfully!");
            } else {
                redirectAttributes.addFlashAttribute("error", "User not found");
            }
        } catch (IllegalArgumentException e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
        }
        return "redirect:/users/list";
    }

    /**
     * Check username availability (AJAX endpoint)
     */
    @GetMapping("/check-username")
    @ResponseBody
    public boolean checkUsernameAvailability(@RequestParam String username) {
        return userService.isUsernameAvailable(username);
    }

    /**
     * Check email availability (AJAX endpoint)
     */
    @GetMapping("/check-email")
    @ResponseBody
    public boolean checkEmailAvailability(@RequestParam String email) {
        return userService.isEmailAvailable(email);
    }
}
